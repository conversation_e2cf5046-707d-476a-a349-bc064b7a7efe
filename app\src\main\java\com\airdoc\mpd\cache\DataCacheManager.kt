package com.airdoc.mpd.cache

import android.content.Context
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.detection.bean.*
import com.airdoc.mpd.detection.repository.SignalResultRepository
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean

/**
 * FileName: DataCacheManager
 * Author: AI Assistant
 * Date: 2025/1/21
 * PS: 数据缓存管理器，负责自动定时上传数据缓存
 */
object DataCacheManager {

    private val TAG = DataCacheManager::class.java.simpleName
    
    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 定时上传任务
    private var uploadJob: Job? = null
    
    // 是否正在上传
    private val isUploading = AtomicBoolean(false)
    
    // 上传间隔时间（毫秒）- 默认30分钟
    private const val UPLOAD_INTERVAL = 30 * 60 * 1000L
    
    // 最小上传间隔时间（毫秒）- 防止频繁上传
    private const val MIN_UPLOAD_INTERVAL = 5 * 60 * 1000L
    
    // 上次上传时间
    private var lastUploadTime = 0L

    // SignalResult数据仓库
    private val signalResultRepository by lazy { SignalResultRepository() }

    /**
     * 启动自动上传服务
     */
    fun startAutoUpload(context: Context) {
        Logger.d(TAG, msg = "启动数据缓存自动上传服务")
        
        // 取消之前的任务
        stopAutoUpload()
        
        // 启动定时上传任务
        uploadJob = coroutineScope.launch {
            while (isActive) {
                try {
                    // 检查是否有数据需要上传
                    val cacheDataCount = getCacheDataCount()
                    if (cacheDataCount > 0) {
                        Logger.d(TAG, msg = "检测到 $cacheDataCount 条缓存数据，准备自动上传")
                        
                        // 检查上传间隔
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastUploadTime >= MIN_UPLOAD_INTERVAL) {
                            tryAutoUpload(context)
                        } else {
                            Logger.d(TAG, msg = "距离上次上传时间过短，跳过本次上传")
                        }
                    }
                    
                    // 等待下次检查
                    delay(UPLOAD_INTERVAL)
                    
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "自动上传任务异常: ${e.message}")
                    // 发生异常时等待较短时间后重试
                    delay(MIN_UPLOAD_INTERVAL)
                }
            }
        }
    }

    /**
     * 停止自动上传服务
     */
    fun stopAutoUpload() {
        Logger.d(TAG, msg = "停止数据缓存自动上传服务")
        uploadJob?.cancel()
        uploadJob = null
    }

    /**
     * 尝试自动上传
     */
    private suspend fun tryAutoUpload(context: Context) {
        if (isUploading.get()) {
            Logger.d(TAG, msg = "正在上传中，跳过本次自动上传")
            return
        }

        try {
            isUploading.set(true)
            Logger.d(TAG, msg = "开始自动上传数据缓存...")

            // 从MMKV获取并上传SignalResult数据
            val uploadSuccess = withContext(Dispatchers.IO) {
                uploadSignalResultsFromCache()
            }

            if (uploadSuccess) {
                // 上传成功后清理缓存数据
                clearDataCache()
                lastUploadTime = System.currentTimeMillis()
                Logger.d(TAG, msg = "自动上传数据缓存完成")
            } else {
                Logger.w(TAG, msg = "自动上传数据缓存部分失败")
            }

        } catch (e: Exception) {
            Logger.e(TAG, msg = "自动上传数据缓存失败: ${e.message}")
        } finally {
            isUploading.set(false)
        }
    }

    /**
     * 手动上传数据缓存
     */
    suspend fun manualUpload(context: Context): Boolean {
        return try {
            if (isUploading.get()) {
                Logger.d(TAG, msg = "正在上传中，请稍后再试")
                false
            } else {
                tryAutoUpload(context)
                true
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "手动上传异常: ${e.message}")
            false
        }
    }

    /**
     * 获取缓存数据条数
     */
    fun getCacheDataCount(): Int {
        var count = 0
        try {
            // 获取HRV评估结果数据条数
            val hrvData = MMKVManager.decodeString(CommonPreference.HRV_ASSESSMENT_RESULT)
            if (!hrvData.isNullOrEmpty()) {
                try {
                    val jsonObject = JSONObject(hrvData)
                    if (jsonObject.has("totalCount")) {
                        count += jsonObject.getInt("totalCount")
                    } else if (jsonObject.has("assessmentResults")) {
                        val resultArray = jsonObject.getJSONArray("assessmentResults")
                        count += resultArray.length()
                    } else {
                        // 如果是旧格式的单个对象，计为1条
                        count += 1
                    }
                } catch (e: Exception) {
                    Logger.w(TAG, msg = "解析HRV数据异常，按1条计算: ${e.message}")
                    count += 1
                }
            }
            
            // 这里可以添加其他类型的缓存数据统计
            // 例如：其他检测结果、用户数据等
            
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算缓存数据条数异常: ${e.message}")
        }
        return count
    }

    /**
     * 清理数据缓存
     */
    private fun clearDataCache() {
        try {
            Logger.d(TAG, msg = "开始清理数据缓存...")

            // 清理HRV评估结果数据
            MMKVManager.encodeString(CommonPreference.HRV_ASSESSMENT_RESULT, null)
            Logger.d(TAG, msg = "已清理HRV评估结果数据")

            // 这里可以添加其他类型的缓存数据清理
            // 例如：其他检测结果、用户数据等

            Logger.d(TAG, msg = "数据缓存清理完成")

        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理数据缓存异常: ${e.message}")
        }
    }

    /**
     * 检查是否正在上传
     */
    fun isUploading(): Boolean {
        return isUploading.get()
    }

    /**
     * 获取上次上传时间
     */
    fun getLastUploadTime(): Long {
        return lastUploadTime
    }

    /**
     * 释放资源
     */
    fun release() {
        stopAutoUpload()
        coroutineScope.cancel()
    }

    /**
     * 从MMKV缓存中上传SignalResult数据
     */
    private suspend fun uploadSignalResultsFromCache(): Boolean {
        return try {
            Logger.d(TAG, msg = "开始从MMKV缓存上传SignalResult数据")

            // 从MMKV获取HRV评估结果数据
            val hrvData = MMKVManager.decodeString(CommonPreference.HRV_ASSESSMENT_RESULT)
            if (hrvData.isNullOrEmpty()) {
                Logger.w(TAG, msg = "HRV评估结果数据为空，无需上传")
                return true
            }

            // 解析JSON数据为SignalResult列表
            val signalResults = parseHrvDataToSignalResults(hrvData)
            if (signalResults.isEmpty()) {
                Logger.w(TAG, msg = "解析SignalResult列表为空，无需上传")
                return true
            }

            Logger.d(TAG, msg = "解析到 ${signalResults.size} 条SignalResult数据，开始上传")

            // 逐个上传SignalResult
            var successCount = 0
            for ((index, signalResult) in signalResults.withIndex()) {
                try {
                    Logger.d(TAG, msg = "上传第 ${index + 1}/${signalResults.size} 条SignalResult数据")
                    val response = signalResultRepository.uploadSignalResult(signalResult)

                    if (response.isSuccess()) {
                        successCount++
                        Logger.d(TAG, msg = "第 ${index + 1} 条SignalResult数据上传成功")
                    } else {
                        Logger.e(TAG, msg = "第 ${index + 1} 条SignalResult数据上传失败: ${response.errorMsg}")
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "第 ${index + 1} 条SignalResult数据上传异常: ${e.message}")
                }
            }

            Logger.d(TAG, msg = "SignalResult数据上传完成，成功: $successCount/${signalResults.size}")

            // 如果全部上传成功，返回true
            successCount == signalResults.size

        } catch (e: Exception) {
            Logger.e(TAG, msg = "从MMKV缓存上传SignalResult数据异常: ${e.message}")
            false
        }
    }

    /**
     * 解析HRV数据为SignalResult列表
     */
    private fun parseHrvDataToSignalResults(hrvData: String): List<SignalResult> {
        return try {
            Logger.d(TAG, msg = "开始解析HRV数据为SignalResult列表")

            val jsonObject = JSONObject(hrvData)
            val signalResults = mutableListOf<SignalResult>()

            // 检查数据格式
            if (jsonObject.has("assessmentResults")) {
                // 新格式：包含数组
                val resultArray = jsonObject.getJSONArray("assessmentResults")
                Logger.d(TAG, msg = "检测到新格式数据，包含 ${resultArray.length()} 条评估结果")

                for (i in 0 until resultArray.length()) {
                    val assessmentResult = resultArray.getJSONObject(i)
                    val signalResult = createSignalResultFromHrvData(assessmentResult, i)
                    signalResults.add(signalResult)
                }
            } else {
                // 旧格式：单个对象
                Logger.d(TAG, msg = "检测到旧格式数据，单个评估结果")
                val signalResult = createSignalResultFromHrvData(jsonObject, 0)
                signalResults.add(signalResult)
            }

            Logger.d(TAG, msg = "成功解析 ${signalResults.size} 条SignalResult数据")
            signalResults

        } catch (e: Exception) {
            Logger.e(TAG, msg = "解析HRV数据为SignalResult列表异常: ${e.message}")
            emptyList()
        }
    }

    /**
     * 从HRV数据创建SignalResult对象
     */
    private fun createSignalResultFromHrvData(hrvJsonObject: JSONObject, index: Int): SignalResult {
        // 创建PPG结果数据
        val ppgResult = createPpgResultFromHrv(hrvJsonObject)

        // 创建用户信息
        val userInfo = createUserInfoFromCache()

        // 创建凝视点数据（示例数据）
        val gazePoints = createSampleGazePoints()

        // 创建信号结果数据（示例数据）
        val signalResultData = listOf(
            listOf(1.0, 2.0, 3.0),
            listOf(4.0, 5.0, 6.0),
            listOf(7.0, 8.0, 9.0)
        )

        return SignalResult(
            signalResult = signalResultData,
            faceEntityId = "face_${System.currentTimeMillis()}_$index",
            gazePoints = gazePoints,
            ppgResult = ppgResult,
            userInfo = userInfo
        )
    }

    /**
     * 从HRV数据创建PpgResult对象
     */
    private fun createPpgResultFromHrv(hrvJsonObject: JSONObject): PpgResult {
        // 解析时域参数
        val timeDomain = TimeDomain(
            meanNN = hrvJsonObject.optDouble("meanNN", 800.0),
            pnn50 = hrvJsonObject.optDouble("pnn50", 15.0),
            rmssd = hrvJsonObject.optDouble("rmssd", 40.0),
            sdnn = hrvJsonObject.optDouble("sdnn", 50.0),
            sdsd = hrvJsonObject.optDouble("sdsd", 35.0)
        )

        // 解析频域参数
        val frequencyDomain = FrequencyDomain(
            hf = hrvJsonObject.optDouble("hf", 300.0),
            lf = hrvJsonObject.optDouble("lf", 500.0),
            lfHfRatio = hrvJsonObject.optDouble("lfHfRatio", 1.67),
            stepPower = listOf(100.0, 200.0, 300.0, 400.0, 500.0),
            totalPower = hrvJsonObject.optDouble("totalPower", 1000.0),
            vlf = hrvJsonObject.optDouble("vlf", 200.0)
        )

        return PpgResult(
            frequencyDomain = frequencyDomain,
            patientId = hrvJsonObject.optInt("patientId", 1001),
            rrIntervals = listOf(800, 820, 790, 810, 805),
            timeDomain = timeDomain,
            totalIntervals = hrvJsonObject.optInt("totalIntervals", 100),
            validIntervals = hrvJsonObject.optInt("validIntervals", 95)
        )
    }

    /**
     * 创建用户信息（从缓存或使用默认值）
     */
    private fun createUserInfoFromCache(): UserInfo {
        return UserInfo(
            id = System.currentTimeMillis(),
            orgId = "org_001",
            patientId = "patient_${System.currentTimeMillis()}",
            username = "cache_user",
            nickname = "缓存用户",
            gender = "未知",
            phone = "13800000000",
            age = 30,
            avatar = null,
            clientType = "ANDROID",
            clientId = "cache_client_${System.currentTimeMillis()}"
        )
    }

    /**
     * 创建示例凝视点数据
     */
    private fun createSampleGazePoints(): List<GazePoint> {
        val currentTime = System.currentTimeMillis()
        return listOf(
            GazePoint(
                startTimeStamp = currentTime - 3000,
                endTimestamp = currentTime - 2000,
                points = emptyList(),
                materialId = "material_001"
            ),
            GazePoint(
                startTimeStamp = currentTime - 2000,
                endTimestamp = currentTime - 1000,
                points = emptyList(),
                materialId = "material_002"
            ),
            GazePoint(
                startTimeStamp = currentTime - 1000,
                endTimestamp = currentTime,
                points = emptyList(),
                materialId = "material_003"
            )
        )
    }
}
